import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';

const QuickActionSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Main navigation items matching reference design
  const navigationItems = [
    {
      label: 'Home',
      icon: 'Home',
      path: '/dashboard',
      isActive: location.pathname === '/dashboard' || location.pathname === '/'
    },
    {
      label: 'Projects',
      icon: 'FolderOpen',
      path: '/dashboard',
      isActive: false
    },
    {
      label: 'Docs',
      icon: 'FileText',
      path: '/document-creator',
      isActive: location.pathname === '/document-creator'
    },
    {
      label: 'Media',
      icon: 'Image',
      path: '/template-library',
      isActive: location.pathname === '/template-library'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };



  return (
    <aside className="fixed left-0 top-0 h-screen w-64 bg-surface border-r border-border z-1000 shadow-sm hidden lg:block">
      <div className="flex flex-col h-full">
        {/* Logo Section */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary to-hero-accent rounded-md flex items-center justify-center">
              <Icon name="FileText" size={18} color="white" />
            </div>
            <span className="text-lg font-semibold text-text-primary">DocForge AI</span>
          </div>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {navigationItems.map((item) => (
              <button
                key={item.path}
                onClick={() => handleNavigation(item.path)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                  item.isActive
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                }`}
              >
                <Icon name={item.icon} size={18} />
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        </nav>

        {/* Upgrade Section */}
        <div className="p-4 border-t border-border">
          <div className="bg-surface-secondary rounded-md p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Zap" size={16} className="text-warning" />
              <span className="text-sm font-medium text-text-primary">Upgrades</span>
            </div>
            <p className="text-xs text-text-secondary mb-3">
              Get access to more features and unlimited projects
            </p>
            <button className="w-full bg-primary text-white text-xs py-2 px-3 rounded-md hover:bg-primary-dark transition-colors duration-200">
              Upgrade Plan
            </button>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default QuickActionSidebar;