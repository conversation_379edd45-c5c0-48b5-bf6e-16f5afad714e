import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const CreationWizard = ({ currentStep, onStepChange, documentData, onDocumentDataChange }) => {
  const [formData, setFormData] = useState({
    documentType: documentData.documentType || 'ebook',
    niche: documentData.niche || '',
    targetAudience: documentData.targetAudience || '',
    keywords: documentData.keywords || '',
    tone: documentData.tone || 'academic',
    language: documentData.language || 'english',
    chapters: documentData.chapters || 5,
    subNiche: documentData.subNiche || ''
  });

  const steps = [
    { id: 1, title: 'Document Type', icon: 'FileText' },
    { id: 2, title: 'Content Details', icon: 'Edit3' },
    { id: 3, title: 'Structure', icon: 'List' },
    { id: 4, title: 'Generate', icon: 'Sparkles' }
  ];

  const documentTypes = [
    { id: 'ebook', name: 'eBook', icon: 'Book', description: 'Digital book with chapters and sections' },
    { id: 'academic', name: 'Academic Paper', icon: 'GraduationCap', description: 'Research papers, essays, theses' },
    { id: 'business', name: 'Business Document', icon: 'Briefcase', description: 'Reports, proposals, presentations' }
  ];

  const toneOptions = [
    { id: 'academic', name: 'Academic', description: 'Formal, scholarly tone' },
    { id: 'conversational', name: 'Conversational', description: 'Friendly, approachable tone' },
    { id: 'professional', name: 'Professional', description: 'Business-appropriate tone' }
  ];

  const languageOptions = [
    { id: 'english', name: 'English', flag: '🇺🇸' },
    { id: 'yoruba', name: 'Yoruba', flag: '🇳🇬' },
    { id: 'french', name: 'French', flag: '🇫🇷' }
  ];

  const handleInputChange = (field, value) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);
    onDocumentDataChange(updatedData);
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      onStepChange(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      onStepChange(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">Choose Document Type</h3>
              <div className="space-y-3">
                {documentTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => handleInputChange('documentType', type.id)}
                    className={`w-full p-4 rounded-lg border-2 transition-micro text-left ${
                      formData.documentType === type.id
                        ? 'border-primary bg-primary/5' :'border-border hover:border-primary/50'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        formData.documentType === type.id ? 'bg-primary text-primary-foreground' : 'bg-background'
                      }`}>
                        <Icon name={type.icon} size={20} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-text-primary">{type.name}</h4>
                        <p className="text-sm text-text-secondary mt-1">{type.description}</p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">Content Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Niche/Subject *
                  </label>
                  <Input
                    type="text"
                    placeholder="e.g., Digital Marketing, Biology, Business Strategy"
                    value={formData.niche}
                    onChange={(e) => handleInputChange('niche', e.target.value)}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Sub-niche (Optional)
                  </label>
                  <Input
                    type="text"
                    placeholder="e.g., Social Media Marketing, Cell Biology"
                    value={formData.subNiche}
                    onChange={(e) => handleInputChange('subNiche', e.target.value)}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Target Audience *
                  </label>
                  <Input
                    type="text"
                    placeholder="e.g., Beginners, University students, Business professionals"
                    value={formData.targetAudience}
                    onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Keywords (comma-separated)
                  </label>
                  <Input
                    type="text"
                    placeholder="e.g., SEO, content marketing, social media"
                    value={formData.keywords}
                    onChange={(e) => handleInputChange('keywords', e.target.value)}
                    className="w-full"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Tone
                    </label>
                    <select
                      value={formData.tone}
                      onChange={(e) => handleInputChange('tone', e.target.value)}
                      className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      {toneOptions.map((tone) => (
                        <option key={tone.id} value={tone.id}>
                          {tone.name} - {tone.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Language
                    </label>
                    <select
                      value={formData.language}
                      onChange={(e) => handleInputChange('language', e.target.value)}
                      className="w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      {languageOptions.map((lang) => (
                        <option key={lang.id} value={lang.id}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">Document Structure</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Number of Chapters/Sections
                  </label>
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => handleInputChange('chapters', Math.max(1, formData.chapters - 1))}
                      className="w-10 h-10 p-0"
                    >
                      <Icon name="Minus" size={16} />
                    </Button>
                    <span className="text-lg font-medium text-text-primary w-12 text-center">
                      {formData.chapters}
                    </span>
                    <Button
                      variant="outline"
                      onClick={() => handleInputChange('chapters', Math.min(20, formData.chapters + 1))}
                      className="w-10 h-10 p-0"
                    >
                      <Icon name="Plus" size={16} />
                    </Button>
                  </div>
                  <p className="text-sm text-text-secondary mt-2">
                    Recommended: 5-10 chapters for eBooks, 3-5 sections for academic papers
                  </p>
                </div>

                <div className="p-4 bg-secondary/10 rounded-lg border border-secondary/20">
                  <div className="flex items-start space-x-3">
                    <Icon name="Info" size={16} color="var(--color-secondary)" />
                    <div>
                      <h4 className="text-sm font-medium text-text-primary">Structure Preview</h4>
                      <p className="text-sm text-text-secondary mt-1">
                        Based on your selections, we'll generate a {formData.chapters}-chapter {formData.documentType} 
                        about "{formData.niche}" in {formData.tone} tone for {formData.targetAudience}.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">Ready to Generate</h3>
              <div className="space-y-4">
                <div className="p-6 bg-success/10 rounded-lg border border-success/20">
                  <div className="flex items-start space-x-3">
                    <Icon name="CheckCircle" size={20} color="var(--color-success)" />
                    <div>
                      <h4 className="font-medium text-text-primary">Configuration Complete</h4>
                      <p className="text-sm text-text-secondary mt-1">
                        Your document is ready to be generated with the following settings:
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-background rounded-lg border border-border">
                    <h5 className="font-medium text-text-primary mb-2">Document Details</h5>
                    <div className="space-y-1 text-sm text-text-secondary">
                      <p><span className="font-medium">Type:</span> {documentTypes.find(t => t.id === formData.documentType)?.name}</p>
                      <p><span className="font-medium">Niche:</span> {formData.niche}</p>
                      <p><span className="font-medium">Audience:</span> {formData.targetAudience}</p>
                    </div>
                  </div>

                  <div className="p-4 bg-background rounded-lg border border-border">
                    <h5 className="font-medium text-text-primary mb-2">Settings</h5>
                    <div className="space-y-1 text-sm text-text-secondary">
                      <p><span className="font-medium">Language:</span> {languageOptions.find(l => l.id === formData.language)?.name}</p>
                      <p><span className="font-medium">Tone:</span> {toneOptions.find(t => t.id === formData.tone)?.name}</p>
                      <p><span className="font-medium">Chapters:</span> {formData.chapters}</p>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-warning/10 rounded-lg border border-warning/20">
                  <div className="flex items-start space-x-3">
                    <Icon name="Zap" size={16} color="var(--color-warning)" />
                    <div>
                      <h5 className="text-sm font-medium text-text-primary">Credit Usage</h5>
                      <p className="text-sm text-text-secondary mt-1">
                        This document will consume approximately 50 credits. You have 250 credits remaining.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Progress Steps */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-micro ${
                  currentStep >= step.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-background border-2 border-border text-text-secondary'
                }`}>
                  {currentStep > step.id ? (
                    <Icon name="Check" size={16} />
                  ) : (
                    <Icon name={step.icon} size={16} />
                  )}
                </div>
                <span className={`text-xs mt-1 ${
                  currentStep >= step.id ? 'text-text-primary font-medium' : 'text-text-secondary'
                }`}>
                  {step.title}
                </span>
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-2 ${
                  currentStep > step.id ? 'bg-primary' : 'bg-border'
                }`} />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 p-4 overflow-y-auto">
        {renderStepContent()}
      </div>

      {/* Navigation Buttons */}
      <div className="p-4 border-t border-border">
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            iconName="ChevronLeft"
            iconPosition="left"
          >
            Previous
          </Button>
          
          {currentStep < steps.length ? (
            <Button
              variant="primary"
              onClick={handleNext}
              iconName="ChevronRight"
              iconPosition="right"
            >
              Next
            </Button>
          ) : (
            <Button
              variant="success"
              onClick={() => console.log('Generate document with:', formData)}
              iconName="Sparkles"
              iconPosition="left"
            >
              Generate Document
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreationWizard;