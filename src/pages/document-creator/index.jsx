import React, { useState, useEffect } from 'react';
import { useSidebar } from '../../contexts/SidebarContext';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import StatusNotification from '../../components/ui/StatusNotification';
import CreationWizard from './components/CreationWizard';
import DocumentOutline from './components/DocumentOutline';
import RichTextEditor from './components/RichTextEditor';
import PlagiarismPanel from './components/PlagiarismPanel';
import VisualSuggestions from './components/VisualSuggestions';
import DocumentToolbar from './components/DocumentToolbar';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const DocumentCreator = () => {
  const { contentMargin } = useSidebar();
  const [activeView, setActiveView] = useState('wizard'); // wizard, editor
  const [leftPanelTab, setLeftPanelTab] = useState('wizard'); // wizard, outline, plagiarism, visuals
  const [documentData, setDocumentData] = useState({
    documentType: 'ebook',
    language: 'english',
    tone: 'academic',
    title: 'Untitled Document'
  });
  const [currentStep, setCurrentStep] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [content, setContent] = useState('');
  const [outline, setOutline] = useState(null);
  const [plagiarismResults, setPlagiarismResults] = useState(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const leftPanelTabs = [
    { id: 'wizard', name: 'Wizard', icon: 'Wand2' },
    { id: 'outline', name: 'Outline', icon: 'List' },
    { id: 'plagiarism', name: 'Plagiarism', icon: 'Shield' },
    { id: 'visuals', name: 'Images', icon: 'Image' }
  ];

  useEffect(() => {
    // Switch to editor view when document generation is complete
    if (currentStep > 4 && activeView === 'wizard') {
      setActiveView('editor');
      setLeftPanelTab('outline');
    }
  }, [currentStep, activeView]);

  const handleDocumentDataChange = (newData) => {
    setDocumentData(prev => ({ ...prev, ...newData }));
  };

  const handleStepChange = (step) => {
    setCurrentStep(step);
    if (step === 4) {
      // Start document generation
      setIsGenerating(true);
      setTimeout(() => {
        setIsGenerating(false);
        setActiveView('editor');
        setLeftPanelTab('outline');
      }, 3000);
    }
  };

  const handleSave = () => {
    setIsSaving(true);
    setTimeout(() => {
      setIsSaving(false);
      console.log('Document saved');
    }, 1500);
  };

  const handleExport = (format) => {
    console.log('Exporting as:', format);
  };

  const handleShare = (option) => {
    console.log('Sharing via:', option);
  };

  const handleStartPlagiarismScan = () => {
    setIsScanning(true);
    setTimeout(() => {
      setIsScanning(false);
      setPlagiarismResults({
        overallScore: 15,
        totalMatches: 8,
        lastScan: new Date()
      });
    }, 3000);
  };

  const handleInsertImage = (image) => {
    console.log('Inserting image:', image);
    // Implementation would insert image into editor
  };

  const renderLeftPanel = () => {
    switch (leftPanelTab) {
      case 'wizard':
        return (
          <CreationWizard
            currentStep={currentStep}
            onStepChange={handleStepChange}
            documentData={documentData}
            onDocumentDataChange={handleDocumentDataChange}
          />
        );
      case 'outline':
        return (
          <DocumentOutline
            outline={outline}
            onOutlineChange={setOutline}
            isGenerating={isGenerating}
          />
        );
      case 'plagiarism':
        return (
          <PlagiarismPanel
            isScanning={isScanning}
            results={plagiarismResults}
            onStartScan={handleStartPlagiarismScan}
          />
        );
      case 'visuals':
        return (
          <VisualSuggestions
            onInsertImage={handleInsertImage}
            currentContext={documentData.niche}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <StatusNotification />
      <QuickActionSidebar />

      <div className={`pt-16 ${contentMargin} transition-all duration-300 ease-in-out`}>
        <div className="p-6">
          <Breadcrumbs />
          
          {/* Page Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-text-primary">Document Creator</h1>
              <p className="text-text-secondary mt-1">
                Create AI-powered documents with guided workflows
              </p>
            </div>
            
            {/* Mobile Menu Toggle */}
            <Button
              variant="outline"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden"
              iconName="Menu"
            >
              Tools
            </Button>
          </div>

          {/* Document Toolbar */}
          {activeView === 'editor' && (
            <DocumentToolbar
              documentType={documentData.documentType}
              onDocumentTypeChange={(type) => handleDocumentDataChange({ documentType: type })}
              language={documentData.language}
              onLanguageChange={(lang) => handleDocumentDataChange({ language: lang })}
              tone={documentData.tone}
              onToneChange={(tone) => handleDocumentDataChange({ tone })}
              onExport={handleExport}
              onSave={handleSave}
              onShare={handleShare}
              isSaving={isSaving}
            />
          )}

          {/* Main Content Area */}
          <div className="flex gap-6 h-[calc(100vh-200px)]">
            {/* Left Panel - Desktop */}
            <div className="hidden lg:block w-1/4 bg-surface rounded-lg border border-border overflow-hidden">
              {/* Panel Tabs */}
              <div className="border-b border-border">
                <div className="flex">
                  {leftPanelTabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setLeftPanelTab(tab.id)}
                      className={`flex-1 px-3 py-3 text-sm font-medium transition-micro flex items-center justify-center space-x-2 ${
                        leftPanelTab === tab.id
                          ? 'bg-primary text-primary-foreground border-b-2 border-primary'
                          : 'text-text-secondary hover:text-text-primary hover:bg-background'
                      }`}
                    >
                      <Icon name={tab.icon} size={16} />
                      <span className="hidden xl:inline">{tab.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Panel Content */}
              <div className="h-full">
                {renderLeftPanel()}
              </div>
            </div>

            {/* Mobile Panel Overlay */}
            {isMobileMenuOpen && (
              <div className="lg:hidden fixed inset-0 z-1100 bg-black/50">
                <div className="absolute right-0 top-0 h-full w-80 bg-surface border-l border-border">
                  <div className="p-4 border-b border-border">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-text-primary">Document Tools</h3>
                      <Button
                        variant="ghost"
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="w-8 h-8 p-0"
                      >
                        <Icon name="X" size={16} />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Mobile Panel Tabs */}
                  <div className="border-b border-border">
                    <div className="grid grid-cols-2 gap-1 p-2">
                      {leftPanelTabs.map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => {
                            setLeftPanelTab(tab.id);
                            setIsMobileMenuOpen(false);
                          }}
                          className={`px-3 py-2 text-sm font-medium rounded-lg transition-micro flex items-center space-x-2 ${
                            leftPanelTab === tab.id
                              ? 'bg-primary text-primary-foreground'
                              : 'text-text-secondary hover:text-text-primary hover:bg-background'
                          }`}
                        >
                          <Icon name={tab.icon} size={16} />
                          <span>{tab.name}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="h-full overflow-hidden">
                    {renderLeftPanel()}
                  </div>
                </div>
              </div>
            )}

            {/* Main Editor Area */}
            <div className="flex-1 bg-surface rounded-lg border border-border overflow-hidden">
              {activeView === 'wizard' ? (
                <div className="h-full flex items-center justify-center p-8">
                  {isGenerating ? (
                    <div className="text-center">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <div className="animate-spin">
                          <Icon name="Loader2" size={32} color="var(--color-primary)" />
                        </div>
                      </div>
                      <h3 className="text-lg font-semibold text-text-primary mb-2">
                        Generating Your Document
                      </h3>
                      <p className="text-text-secondary mb-4">
                        AI is creating your {documentData.documentType} about "{documentData.niche}"
                      </p>
                      <div className="w-64 bg-border rounded-full h-2 mx-auto">
                        <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
                      </div>
                      <p className="text-sm text-text-secondary mt-2">
                        Estimated time: 30 seconds
                      </p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Icon name="FileText" size={32} color="var(--color-secondary)" />
                      </div>
                      <h3 className="text-lg font-semibold text-text-primary mb-2">
                        Ready to Create
                      </h3>
                      <p className="text-text-secondary">
                        Complete the wizard steps to generate your document
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <RichTextEditor
                  content={content}
                  onContentChange={setContent}
                  collaborators={[]}
                  suggestions={[]}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentCreator;