import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import ProjectCard from './components/ProjectCard';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const Projects = () => {
  const navigate = useNavigate();
  const [activeCategory, setActiveCategory] = useState('ebooks');
  const [sortBy, setSortBy] = useState('newest');
  const [isLoading, setIsLoading] = useState(true);

  // Mock projects data - extending from dashboard mockDocuments
  const mockProjects = [
    {
      id: 1,
      title: "Sample Project",
      type: "ebook",
      category: "eBooks",
      status: "completed",
      createdAt: "2025-06-27T10:30:00Z",
      updatedAt: "2025-06-27T10:30:00Z",
      thumbnail: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: []
    },
    {
      id: 2,
      title: "AI-Powered Marketing Strategy for Tech Startups",
      type: "ebook",
      category: "eBooks",
      status: "completed",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
      thumbnail: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Sarah Johnson", email: "<EMAIL>" },
        { name: "Mike Chen", email: "<EMAIL>" }
      ]
    },
    {
      id: 3,
      title: "Research Paper: Machine Learning in Healthcare",
      type: "flipbook",
      category: "Flipbooks",
      status: "draft",
      createdAt: "2024-01-14T14:20:00Z",
      updatedAt: "2024-01-14T14:20:00Z",
      thumbnail: "https://images.pexels.com/photos/3825581/pexels-photo-3825581.jpeg?w=400&h=300&fit=crop",
      progress: 75,
      collaborators: []
    },
    {
      id: 4,
      title: "Complete Guide to Digital Transformation",
      type: "ebook",
      category: "eBooks",
      status: "completed",
      createdAt: "2024-01-13T09:15:00Z",
      updatedAt: "2024-01-13T09:15:00Z",
      thumbnail: "https://images.pixabay.com/photo/2016/11/29/06/15/book-1867171_1280.jpg?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Alex Rivera", email: "<EMAIL>" }
      ]
    },
    {
      id: 5,
      title: "Business Proposal: Green Energy Solutions",
      type: "business",
      category: "Business",
      status: "shared",
      createdAt: "2024-01-12T16:45:00Z",
      updatedAt: "2024-01-12T16:45:00Z",
      thumbnail: "https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Emma Wilson", email: "<EMAIL>" },
        { name: "David Park", email: "<EMAIL>" },
        { name: "Lisa Zhang", email: "<EMAIL>" }
      ]
    },
    {
      id: 6,
      title: "Thesis: Sustainable Urban Development",
      type: "flipbook",
      category: "Flipbooks",
      status: "draft",
      createdAt: "2024-01-11T11:30:00Z",
      updatedAt: "2024-01-11T11:30:00Z",
      thumbnail: "https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?w=400&h=300&fit=crop",
      progress: 60,
      collaborators: []
    },
    {
      id: 7,
      title: "The Future of Remote Work - eBook",
      type: "ebook",
      category: "eBooks",
      status: "completed",
      createdAt: "2024-01-10T13:20:00Z",
      updatedAt: "2024-01-10T13:20:00Z",
      thumbnail: "https://images.pixabay.com/photo/2020/07/08/04/12/work-5382501_1280.jpg?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: []
    },
    {
      id: 8,
      title: "Marketing Strategy Guide",
      type: "business",
      category: "Business",
      status: "draft",
      createdAt: "2024-01-09T15:30:00Z",
      updatedAt: "2024-01-09T15:30:00Z",
      thumbnail: "https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400&h=300&fit=crop",
      progress: 45,
      collaborators: []
    }
  ];

  // Categories for filtering - based on actual functionality
  const categories = [
    { id: 'ebooks', label: 'eBooks (1)', count: mockProjects.filter(p => p.category === 'eBooks').length },
    { id: 'flipbooks', label: 'Flipbooks', count: mockProjects.filter(p => p.category === 'Flipbooks').length },
    { id: 'business', label: 'Business', count: mockProjects.filter(p => p.category === 'Business').length }
  ];

  // Sort options
  const sortOptions = [
    { value: 'newest', label: 'Newest' },
    { value: 'oldest', label: 'Oldest' },
    { value: 'name', label: 'Name' },
    { value: 'progress', label: 'Progress' }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Filter and sort projects
  const filteredProjects = mockProjects
    .filter(project => {
      return project.category.toLowerCase() === activeCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.updatedAt) - new Date(a.updatedAt);
        case 'oldest':
          return new Date(a.updatedAt) - new Date(b.updatedAt);
        case 'name':
          return a.title.localeCompare(b.title);
        case 'progress':
          return b.progress - a.progress;
        default:
          return 0;
      }
    });

  const handleEditProject = (project) => {
    navigate('/document-creator', { state: { documentId: project.id } });
  };

  const handleCreateProject = () => {
    navigate('/document-creator');
  };

  const handleCreateFolder = () => {
    console.log('Creating new folder...');
    // Implement folder creation logic
    // This could open a modal for folder name input
  };

  const handleDuplicateProject = (project) => {
    console.log('Duplicating project:', project.title);
    // Implement duplication logic
  };

  const handleShareProject = (project) => {
    console.log('Sharing project:', project.title);
    navigate('/collaboration-workspace', { state: { documentId: project.id } });
  };

  const handleDeleteProject = (project) => {
    console.log('Deleting project:', project.title);
    // Implement deletion logic with confirmation
    if (window.confirm(`Are you sure you want to delete "${project.title}"? This action cannot be undone.`)) {
      // Remove project from list (in real app, this would be an API call)
      console.log('Project deleted');
    }
  };

  const handlePreviewProject = (project) => {
    console.log('Previewing project:', project.title);
    // Implement preview functionality
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <Header />
        <main className="lg:ml-64 ml-0 pt-16">
          <div className="px-6 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading your projects...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      <Header />

      <main className="lg:ml-64 ml-0 pt-16">
        <div className="px-6 py-8">
          <Breadcrumbs />

          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-text-primary mb-2">Projects</h1>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="secondary"
                  onClick={handleCreateFolder}
                  className="hidden lg:flex rounded-lg"
                >
                  <Icon name="FolderPlus" size={16} />
                  New folder
                </Button>
                <Button
                  variant="primary"
                  onClick={handleCreateProject}
                  className="rounded-lg"
                >
                  <Icon name="Plus" size={16} />
                  Create
                </Button>
              </div>
            </div>

            {/* Category Tabs and Sort Controls */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Category Tabs */}
              <div className="flex items-center space-x-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border ${
                      activeCategory === category.id
                        ? 'bg-primary text-white border-primary shadow-sm'
                        : 'bg-surface text-text-secondary hover:text-text-primary border-border hover:border-primary hover:shadow-sm'
                    }`}
                  >
                    {category.label}
                  </button>
                ))}
              </div>

              {/* Sort Controls */}
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-text-secondary">Sort:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-surface border border-border rounded-lg px-3 py-2 text-sm text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent min-w-[120px]"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onClick={handleEditProject}
                onPreview={handlePreviewProject}
                onDuplicate={handleDuplicateProject}
                onShare={handleShareProject}
                onDelete={handleDeleteProject}
              />
            ))}
          </div>

          {/* Empty State */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <Icon name="FolderOpen" size={48} className="text-text-muted mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                No projects found
              </h3>
              <p className="text-text-secondary mb-6">
                {`No ${activeCategory} projects found. Try a different category or create a new project.`}
              </p>
              <Button
                variant="primary"
                onClick={handleCreateProject}
                className="rounded-lg"
              >
                <Icon name="Plus" size={16} />
                Create Your First Project
              </Button>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Projects;
