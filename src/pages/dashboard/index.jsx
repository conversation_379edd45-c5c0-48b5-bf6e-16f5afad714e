import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import QuickStatsCard from './components/QuickStatsCard';
import DocumentLibrary from './components/DocumentLibrary';
import QuickActions from './components/QuickActions';
import CreditUsageMeter from './components/CreditUsageMeter';
import CollaborationNotifications from './components/CollaborationNotifications';
import ActivityFeed from './components/ActivityFeed';
import ChatWidget from '../../components/ui/ChatWidget';
import Icon from '../../components/AppIcon';

const Dashboard = () => {
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const [isLoading, setIsLoading] = useState(true);

  // Mock data
  const mockDocuments = [
    {
      id: 1,
      title: "AI-Powered Marketing Strategy for Tech Startups",
      type: "business",
      status: "completed",
      createdAt: "2024-01-15T10:30:00Z",
      pageCount: 45,
      thumbnail: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Sarah Johnson", email: "<EMAIL>" },
        { name: "Mike Chen", email: "<EMAIL>" }
      ]
    },
    {
      id: 2,
      title: "Research Paper: Machine Learning in Healthcare",
      type: "academic",
      status: "draft",
      createdAt: "2024-01-14T14:20:00Z",
      pageCount: 28,
      thumbnail: "https://images.pexels.com/photos/3825581/pexels-photo-3825581.jpeg?w=400&h=300&fit=crop",
      progress: 75,
      collaborators: []
    },
    {
      id: 3,
      title: "Complete Guide to Digital Transformation",
      type: "ebook",
      status: "completed",
      createdAt: "2024-01-13T09:15:00Z",
      pageCount: 120,
      thumbnail: "https://images.pixabay.com/photo/2016/11/29/06/15/book-1867171_1280.jpg?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Alex Rivera", email: "<EMAIL>" }
      ]
    },
    {
      id: 4,
      title: "Business Proposal: Green Energy Solutions",
      type: "business",
      status: "shared",
      createdAt: "2024-01-12T16:45:00Z",
      pageCount: 32,
      thumbnail: "https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Emma Wilson", email: "<EMAIL>" },
        { name: "David Park", email: "<EMAIL>" },
        { name: "Lisa Zhang", email: "<EMAIL>" }
      ]
    },
    {
      id: 5,
      title: "Thesis: Sustainable Urban Development",
      type: "academic",
      status: "draft",
      createdAt: "2024-01-11T11:30:00Z",
      pageCount: 85,
      thumbnail: "https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?w=400&h=300&fit=crop",
      progress: 60,
      collaborators: []
    },
    {
      id: 6,
      title: "The Future of Remote Work - eBook",
      type: "ebook",
      status: "completed",
      createdAt: "2024-01-10T13:20:00Z",
      pageCount: 95,
      thumbnail: "https://images.pixabay.com/photo/2020/07/08/04/12/work-5382501_1280.jpg?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: []
    }
  ];

  const mockRecentTemplates = [
    {
      id: 1,
      name: "Research Paper Template",
      category: "Academic",
      icon: "FileText"
    },
    {
      id: 2,
      name: "Business Report Template",
      category: "Business",
      icon: "BarChart3"
    },
    {
      id: 3,
      name: "eBook Template",
      category: "Publishing",
      icon: "Book"
    }
  ];

  const mockCollaborationNotifications = [
    {
      id: 1,
      inviterName: "Sarah Johnson",
      documentTitle: "Market Analysis Report Q1 2024",
      timestamp: "2024-01-15T08:30:00Z",
      type: "collaboration_invite"
    },
    {
      id: 2,
      inviterName: "Dr. Michael Chen",
      documentTitle: "Research Methodology Guidelines",
      timestamp: "2024-01-14T15:45:00Z",
      type: "collaboration_invite"
    }
  ];

  const mockActivityFeed = [
    {
      id: 1,
      type: "document_created",
      description: "Created new business document 'Marketing Strategy'",
      timestamp: "2024-01-15T10:30:00Z"
    },
    {
      id: 2,
      type: "collaboration_invite",
      description: "Sarah Johnson invited you to collaborate",
      timestamp: "2024-01-15T08:30:00Z"
    },
    {
      id: 3,
      type: "plagiarism_check",
      description: "Completed plagiarism check for 'Research Paper'",
      timestamp: "2024-01-14T16:20:00Z"
    },
    {
      id: 4,
      type: "template_used",
      description: "Used \'Academic Paper\' template",
      timestamp: "2024-01-14T14:15:00Z"
    },
    {
      id: 5,
      type: "document_shared",
      description: "Shared \'Business Proposal\' with team",
      timestamp: "2024-01-13T12:45:00Z"
    }
  ];

  const mockUsageHistory = [
    { activity: "Document Generation", credits: 15, icon: "FileText" },
    { activity: "AI Content Enhancement", credits: 8, icon: "Wand2" },
    { activity: "Plagiarism Check", credits: 5, icon: "Shield" },
    { activity: "Template Customization", credits: 3, icon: "Settings" }
  ];

  // Stats calculations
  const totalDocuments = mockDocuments.length;
  const draftDocuments = mockDocuments.filter(doc => doc.status === 'draft').length;
  const completedDocuments = mockDocuments.filter(doc => doc.status === 'completed').length;
  const sharedDocuments = mockDocuments.filter(doc => doc.status === 'shared').length;
  const collaborationInvites = mockCollaborationNotifications.length;
  const plagiarismScans = mockActivityFeed.filter(activity => activity.type === 'plagiarism_check').length;

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleEditDocument = (document) => {
    navigate('/document-creator', { state: { documentId: document.id } });
  };

  const handleDuplicateDocument = (document) => {
    console.log('Duplicating document:', document.title);
    // Implement duplication logic
  };

  const handleShareDocument = (document) => {
    console.log('Sharing document:', document.title);
    navigate('/collaboration-workspace', { state: { documentId: document.id } });
  };

  const handleDeleteDocument = (document) => {
    console.log('Deleting document:', document.title);
    // Implement deletion logic with confirmation
  };

  const handleAcceptCollaboration = async (notification) => {
    console.log('Accepting collaboration:', notification.documentTitle);
    // Implement accept logic
    return Promise.resolve();
  };

  const handleDeclineCollaboration = async (notification) => {
    console.log('Declining collaboration:', notification.documentTitle);
    // Implement decline logic
    return Promise.resolve();
  };

  const handleViewAllCollaborations = () => {
    navigate('/collaboration-workspace');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <Header />
        <main className="lg:ml-64 ml-0 pt-16">
          <div className="px-6 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading your dashboard...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />
      <Header />

      <main className={`${contentMargin} ml-0 pt-16 transition-all duration-300 ease-in-out`}>
        <div className="px-6 py-8">

          {/* Quick Action Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* New eBook Card */}
            <div className="bg-surface rounded-lg border border-border p-6 hover:shadow-elevated transition-all duration-300 cursor-pointer group">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-light rounded-md flex items-center justify-center">
                    <Icon name="BookOpen" size={20} className="text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary group-hover:text-primary transition-colors">New eBook</h3>
                    <p className="text-sm text-text-secondary">Start writing on a blank page or import</p>
                  </div>
                </div>
                <Icon name="ArrowRight" size={16} className="text-text-muted group-hover:text-primary transition-colors" />
              </div>
            </div>

            {/* New Audiobook Card */}
            <div className="bg-surface rounded-lg border border-border p-6 hover:shadow-elevated transition-all duration-300 cursor-pointer group">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-accent-light rounded-md flex items-center justify-center">
                    <Icon name="Volume2" size={20} className="text-accent" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-text-primary group-hover:text-accent transition-colors">New Audiobook</h3>
                    <p className="text-sm text-text-secondary">Start writing on a blank page or import</p>
                  </div>
                </div>
                <Icon name="ArrowRight" size={16} className="text-text-muted group-hover:text-accent transition-colors" />
              </div>
              <div className="flex items-center space-x-2 text-xs">
                <span className="bg-primary text-white px-2 py-1 rounded">Drag and drop or</span>
                <button className="text-primary hover:underline">browse to import</button>
                <span className="text-text-muted">pdf, docx, .txt</span>
              </div>
            </div>
          </div>

          {/* Hero Section - Matching reference design */}
          <div className="mb-12">
            <div className="bg-gradient-to-r from-hero-start via-hero-middle to-hero-end rounded-xl p-8 lg:p-12 text-white relative overflow-hidden">
              <div className="relative z-10 flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-4">
                    <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                      ✨ AI-Powered
                    </span>
                  </div>
                  <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                    Meet your AI-powered eBook creator
                  </h1>
                  <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    Wordgenie™
                  </h2>
                  <button
                    onClick={() => navigate('/document-creator')}
                    className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-md font-semibold transition-all duration-300 transform hover:-translate-y-1 shadow-elevated"
                  >
                    Try it now →
                  </button>
                </div>

                {/* Hero Image/Illustration */}
                <div className="hidden lg:block flex-shrink-0 ml-8">
                  <div className="w-64 h-48 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <Icon name="Sparkles" size={48} className="mx-auto mb-4 text-white/80" />
                      <p className="text-sm text-white/70">Generate text with Wordgenie</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            </div>
          </div>

          {/* More options to create an eBook - Matching reference design */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">More options to create an eBook</h3>

            <div className="flex space-x-4 overflow-x-auto pb-4">
              {[
                {
                  icon: 'FileText',
                  label: 'Doc',
                  description: 'PDF to Flipbook',
                  color: 'bg-red-50 border-red-200',
                  iconColor: 'text-red-500'
                },
                {
                  icon: 'FileText',
                  label: 'Start from scratch',
                  description: '',
                  color: 'bg-blue-50 border-blue-200',
                  iconColor: 'text-blue-500'
                },
                {
                  icon: 'Link',
                  label: 'Import from blog post or URL',
                  description: '',
                  color: 'bg-purple-50 border-purple-200',
                  iconColor: 'text-purple-500'
                },
                {
                  icon: 'FileText',
                  label: 'Start from template',
                  description: '',
                  color: 'bg-green-50 border-green-200',
                  iconColor: 'text-green-500'
                },
                {
                  icon: 'FileText',
                  label: 'Import from DOCX',
                  description: '',
                  color: 'bg-orange-50 border-orange-200',
                  iconColor: 'text-orange-500'
                },
                {
                  icon: 'FileText',
                  label: 'Import from PDF',
                  description: '',
                  color: 'bg-pink-50 border-pink-200',
                  iconColor: 'text-pink-500'
                },
                {
                  icon: 'FileText',
                  label: 'Import from...',
                  description: '',
                  color: 'bg-gray-50 border-gray-200',
                  iconColor: 'text-gray-500'
                }
              ].map((option, index) => (
                <div
                  key={index}
                  className={`flex-shrink-0 w-32 h-32 ${option.color} border-2 border-dashed rounded-lg flex flex-col items-center justify-center p-4 cursor-pointer hover:shadow-md transition-all duration-300 group`}
                >
                  <Icon name={option.icon} size={24} className={`${option.iconColor} mb-2 group-hover:scale-110 transition-transform`} />
                  <p className="text-xs text-center font-medium text-text-primary leading-tight">{option.label}</p>
                  {option.description && (
                    <p className="text-xs text-center text-text-muted mt-1">{option.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recent Projects - Matching reference design */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">Recent projects</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {mockDocuments.slice(0, 4).map((document) => (
                <div
                  key={document.id}
                  className="bg-surface rounded-lg border border-border hover:shadow-elevated transition-all duration-300 cursor-pointer group overflow-hidden"
                  onClick={() => handleEditDocument(document)}
                >
                  {/* Document Thumbnail */}
                  <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                    <img
                      src={document.thumbnail}
                      alt={document.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />

                    {/* Status Badge */}
                    <div className={`absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ${
                      document.status === 'completed' ? 'bg-success text-white' :
                      document.status === 'draft' ? 'bg-warning text-white' :
                      'bg-primary text-white'
                    }`}>
                      {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                    </div>
                  </div>

                  {/* Document Info */}
                  <div className="p-4">
                    <h4 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300 line-clamp-2 mb-2">
                      {document.title}
                    </h4>
                    <div className="flex items-center justify-between text-sm text-text-secondary">
                      <span className="capitalize">{document.type}</span>
                      <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                    </div>

                    {document.progress < 100 && (
                      <div className="mt-3">
                        <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                          <span>Progress</span>
                          <span>{document.progress}%</span>
                        </div>
                        <div className="w-full bg-surface-secondary rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{ width: `${document.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>


        </div>
      </main>

      {/* Chat Widget */}
      <ChatWidget />
    </div>
  );
};

export default Dashboard;